# Hosting Parsons on Github Template
This repo is a template to help you quickly and easily host <PERSON><PERSON>'s problems on GitHub.

You can use [Codio's graphical <PERSON><PERSON>'s problems generator](https://codio.github.io/parsons-puzzle-ui/dist/) to create the <PERSON><PERSON>'s problems and paste them into this template. [Visit the other repo's main page for help on using the generator.](https://codio.github.io/parsons-puzzle-ui/)

## How to host your own <PERSON><PERSON>'s Problems

1. Create a Github account (if you don't have one already). A free account works great for this!

1. Fork this repo using the "Fork" button 

    ![Fork Button](https://sammyk.s3.amazonaws.com/blog/images/2014-05-28/fork.png)
    
1. In **your fork** (it should have your user name after the `github.com/` in the URL bar) click on "Settings":

    ![Settings Tab](https://pages.github.com/images/<EMAIL>)
    
1. Set the GitHub Pages Source to Master branch using the drop down:

    ![Set GitHub Pages Source](https://pages.github.com/images/<EMAIL>)
    
### Test your repo

1. To check that your repo is now setup correctly, return to the "Code" tab on the far left (you can get there by clicking on the repo name at the top too). 

1. Click on the environment button with the rocketship icon on the right-side of the page

    ![Click github-pages link on the right-side of the page](https://raw.githubusercontent.com/codio-content/hosting-parsons-on-github-template/master/Environments.png)

1. Click on the "View Deployment" button - the one at the top is the most recent.

    ![Click on the "View Deployment" button](https://raw.githubusercontent.com/codio-content/hosting-parsons-on-github-template/master/Deployments.png)
    
## How to Add Generated Parson's Problems

1. Use [Codio's graphical Parson's problems generator](https://codio.github.io/parsons-puzzle-ui/dist/) to create a Parson's problem

1. Click EXPORT in the top left

1. Click Switch to Code

1. (optional) If you are hosting multiple parsons problems on the same page (in the same markdown) then add a unique pre-fix in the textbox at the top and press enter.

1. Copy the code into index.markdown

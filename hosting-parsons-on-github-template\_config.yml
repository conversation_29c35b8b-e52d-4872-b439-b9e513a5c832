# Welcome to <PERSON><PERSON><PERSON>!
#
# This config file is meant for settings that affect your whole blog, values
# which you are expected to set up once and rarely edit after that. If you find
# yourself editing this file very often, consider using <PERSON><PERSON><PERSON>'s data files
# feature for the data you need to update frequently.
#
# For technical reasons, this file is *NOT* reloaded automatically when you use
# 'bundle exec jekyll serve'. If you change this file, please restart the server process.
#
# If you need help with YAML syntax, here are some quick references for you:
# https://learn-the-web.algonquindesign.ca/topics/markdown-yaml-cheat-sheet/#yaml
# https://learnxinyminutes.com/docs/yaml/
#
# Site settings
# These are used to personalize your new site. If you look in the HTML files,
# you will see them accessed via {{ site.title }}, {{ site.email }}, and so on.
# You can create any custom variable you would like, and they will be accessible
# in the templates via {{ site.myvariable }}.

title: Parsons UI Test für DHBW Projekt
email: <EMAIL>
description: 
  <PERSON><PERSON><PERSON> oder mische die Codeblöcke in den folgenden Übungsaufgaben.
  Achte da<PERSON>, dort e<PERSON>, wo es erford<PERSON>lich ist, indem du die Blöcke nach rechts ziehst. <br/>

  Um deine Lösung zu überprüfen, klicke auf die Schaltfläche „Get Feedback“.
  Um neu zu beginnen, klicke auf die Schaltfläche „Reset Problem“.
baseurl: "/hosting-parsons-on-github-template" # the subpath of your site, e.g. /blog
url: "" # the base hostname & protocol for your site, e.g. http://example.com
twitter_username:
github_username:

# Build settings
theme: jekyll-theme-cayman
plugins:
  - jekyll-feed

# Exclude from processing.
# The following items will not be processed, by default.
# Any item listed under the `exclude:` key here will be automatically added to
# the internal "default list".
#
# Excluded items can be processed by explicitly listing the directories or
# their entries' file path in the `include:` list.
#
# exclude:
#   - .sass-cache/
#   - .jekyll-cache/
#   - gemfiles/
#   - Gemfile
#   - Gemfile.lock
#   - node_modules/
#   - vendor/bundle/
#   - vendor/cache/
#   - vendor/gems/
#   - vendor/ruby/
